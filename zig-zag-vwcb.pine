

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © Dev Lucem

//@version=5
//@author=devlucem

//
//       THIS CODE IS BASED FROM THE MT4 ZIGZAG INDICATOR
//       THE <PERSON><PERSON><PERSON><PERSON> SETTINGS FOR THE MAIN ONE ON TRADINGVIEW DO NOT WORK THE SAME AS MT4
//       I HOPE U LOVE IT
//


////////
// Seek Menu
import DevLucem/ZigLib/1 as ZigZag
indicator('ZigZag++ | Impulse/Correction', 'ZPIC', true, format.price, max_labels_count=200, max_lines_count=50)

////////
// Fetch Ingredients 
// [
Depth = input.int(12, 'Depth', minval=1, step=1, group="ZigZag Config")
Deviation = input.int(5, 'Deviation', minval=1, step=1, group="ZigZag Config")
Backstep = input.int(2, 'Backstep', minval=2, step=1, group="ZigZag Config")
line_thick = input.int(2, 'Line Thickness', minval=1, maxval=4, group="Lines")
labels = input(0, "Labels Transparency", group="Labels")
upcolor = input(color.lime, 'Bull Color', group="Colors")
dncolor = input(color.red, 'Bear Color', group="Colors")
lines = input(0, "Lines Transparency", group="Lines")
background = input(80, "Background Transparency", group="Colors")
label_size = switch input.int(3, "Label SIze", minval=1, maxval=5, group="Labels")
    1 => size.tiny
    2 => size.small
    3 => size.normal
    4 => size.large
    5 => size.huge
repaint = input(true, 'Repaint Levels')
extend = input(false, "Extend ZigZag", group="Lines")

// Impulse/Correction Settings
group_impulse = "Impulse/Correction Analysis"
enableImpulseAnalysis = input.bool(true, "Enable Impulse/Correction Analysis", group=group_impulse)
atrLen = input.int(14, "ATR Length", minval=1, group=group_impulse)
impulseRATR = input.float(1.5, "Impulse Body Threshold (×ATR)", minval=0.5, step=0.1, group=group_impulse)
corrRATR = input.float(0.8, "Corrective Body Threshold (×ATR)", minval=0.1, step=0.1, group=group_impulse)
wickRatio = input.float(0.4, "Long Wick Ratio", minval=0.1, maxval=0.8, step=0.1, group=group_impulse)
rsiLen = input.int(14, "RSI Length", minval=2, group=group_impulse)
rsiNeutralHi = input.int(60, "RSI Neutral High", minval=50, maxval=80, group=group_impulse)
rsiNeutralLo = input.int(40, "RSI Neutral Low", minval=20, maxval=50, group=group_impulse)
showEntryAlerts = input.bool(true, "Show Entry Alerts", group=group_impulse)
// ]


// ////////
// // Bake it with a simple oven this time
[direction, z1, z2] = ZigZag.zigzag(low, high, Depth, Deviation, Backstep)
string nowPoint = ""
var float lastPoint = z1.price[1]
if bool(ta.change(direction))
    lastPoint := z1.price[1]


// ////////
// // Let it Cool And Serve
line zz = na
label point = na

if repaint
    zz := line.new(z1, z2, xloc.bar_time, extend? extend.right: extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
    nowPoint := direction<0? (z2.price<lastPoint? "LL": "HL"): (z2.price>lastPoint? "HH": "LH")
    point := label.new(z2, nowPoint, xloc.bar_time, yloc.price, 
     color.new(direction<0? upcolor: dncolor, labels), direction>0? label.style_label_down: label.style_label_up, color.new(direction>0? upcolor: dncolor, labels), label_size)
    if direction == direction[1]
        line.delete(zz[1])
        label.delete(point[1])
    else
        line.set_extend(zz[1], extend.none)
else
    if direction != direction[1]
        zz := line.new(z1[1], z2[1], xloc.bar_time, extend.none, color.new(direction>0? upcolor: dncolor, lines), width=line_thick)
        nowPoint := direction[1]<0? (z2.price[1]<lastPoint[1]? "LL": "HL"): (z2.price[1]>lastPoint[1]? "HH": "LH")
        point := label.new(z2[1], nowPoint, xloc.bar_time, yloc.price,
         color.new(direction[1]<0? upcolor: dncolor, labels), direction[1]>0? label.style_label_down: label.style_label_up, color.new(direction[1]>0? upcolor: dncolor, labels), label_size)

// ───── NEW: Impulse vs Correction Engine ────────────────────────
// ---- Helpers ----
atr_ = ta.atr(atrLen)
body = math.abs(close - open)
range_ = high - low
rsi_ = ta.rsi(close, rsiLen)

// ---- Classification Functions ----
inImpulse(bar) =>
    // Safety check to prevent accessing beyond historical buffer
    if bar >= bar_index
        false
    else
        strongBody = body[bar] > impulseRATR * atr_[bar]
        nearExtrem = math.abs(close[bar] - (close[bar] > open[bar] ? high[bar] : low[bar])) / range_[bar] < 0.2
        momUp = rsi_[bar] > rsiNeutralHi
        momDn = rsi_[bar] < rsiNeutralLo
        strongBody and nearExtrem and (momUp or momDn)

inCorrection(bar) =>
    // Safety check to prevent accessing beyond historical buffer
    if bar >= bar_index
        false
    else
        smallBody = body[bar] < corrRATR * atr_[bar]
        longWick = (math.max(high[bar] - math.max(open[bar], close[bar]), math.min(open[bar], close[bar]) - low[bar])) / range_[bar] > wickRatio
        neutralRS = rsi_[bar] > rsiNeutralLo and rsi_[bar] < rsiNeutralHi
        smallBody or longWick or neutralRS

// ---- Swing classification arrays ----
var swingIsImpulse = array.new_bool()
var swingStartBar = array.new_int()

// Calculate impulse/correction for current bar (for consistency)
currentBarIsImpulse = enableImpulseAnalysis ? inImpulse(0) : false
currentBarIsCorrection = enableImpulseAnalysis ? inCorrection(0) : false

// Calculate direction change for consistency
directionChanged = ta.change(direction) != 0

// ───── Walk every confirmed swing ────────────────────────────────
if enableImpulseAnalysis and directionChanged
    // Determine start & end bar indices
    int endBar = bar_index
    int startBar = array.size(swingStartBar) == 0 ? 0 : array.get(swingStartBar, array.size(swingStartBar)-1)

    // Count how many bars in this swing
    int barsInSwing = math.max(endBar - startBar, 1)

    // Limit lookback to available historical data (bar_index gives us current bar position)
    int maxLookback = math.min(barsInSwing - 1, bar_index - 1)

    // Count how many bars satisfy impulse vs correction criteria
    int impulseCnt = 0
    int correctionCnt = 0
    for i = 0 to maxLookback
        if inImpulse(i)
            impulseCnt += 1
        if inCorrection(i)
            correctionCnt += 1

    // Majority wins
    bool isImp = impulseCnt > correctionCnt
    array.push(swingIsImpulse, isImp)
    array.push(swingStartBar, endBar)

// ───── Plot background for latest swing ─────────────────────────
color bgCol = na
if enableImpulseAnalysis and array.size(swingIsImpulse) >= 2
    if array.get(swingIsImpulse, array.size(swingIsImpulse)-2)
        bgCol := color.new(color.green, background)
    else
        bgCol := color.new(color.yellow, background)

bgcolor(bgCol, title="Impulse / Correction Background")

// ───── Entry Alert: correction just ended ───────────────────────
var bool wasCorrection = false
if enableImpulseAnalysis and array.size(swingIsImpulse) >= 2 and showEntryAlerts
    bool prevSwingWasCorr = not array.get(swingIsImpulse, array.size(swingIsImpulse)-2)
    bool currSwingIsImp = array.get(swingIsImpulse, array.size(swingIsImpulse)-1)
    if prevSwingWasCorr and currSwingIsImp and wasCorrection == false
        label.new(bar_index, low, "Correction END\nEntry?", style=label.style_label_up, color=color.aqua, textcolor=color.black, size=size.small)
        alert("ZigZag Correction ended – possible pullback entry on " + syminfo.ticker, alert.freq_once_per_bar)
    wasCorrection := not currSwingIsImp





// indicator(title = 'Volume Weighted Colored Bars with Exhaustion & Volatility', overlay = true, max_lines_count = 500)

// -Definitions ════════════════════════════════════════════════════════════════════════════════ //
group_volume_weighted_colored_bars      = 'Volume Weighted Colored Bars'
tooltip_volume_weighted_colored_bars    = 'Colors bars based on the bar\'s volume relative to volume moving average\n' +
                                          'trading tip : a potential breakout trading opportunity may occur when price moves above a resistance level or moves below a support level on increasing volume'

group_volume_spike_sign_of_exhaustion   = 'Volume Spike - Sign of Exhaustion'
tooltip_volume_spike_sign_of_exhaustion = 'Moments where\n' +
                                          'huge volume detected : current volume is grater than the product of the theshold value and volume moving average\n' +
                                          'presents idea : huge volume may be a sign of exhaustion and may lead to sharp reversals'

group_high_volatility                   = 'High Volatility'
tooltip_high_volatility                 = 'Moments where\n' +
                                           'price range of the current bar is grater than the product of the theshold value and average true range value of defined period'

tooltip_volume_moving_average           = 'Volume simple moving average, serves as reference to Volume Weighted Colored Bars calculations'

// -Inputs ════════════════════════════════════════════════════════════════════════════════════ //
// General Settings
i_vSMA_length = input.int(89, 'Volume Moving Average Length', group='General Settings', tooltip=tooltip_volume_moving_average)
srLookbackRange = input.string('Visible Range', 'Lookback Range', options = ['Fixed Range', 'Visible Range'], group='General Settings')
i_lenLookback = input.int(360, 'Fixed Range : Lookback Interval (Bars)', minval=0, step=10, group='General Settings')

// Volume Weighted Colored Bars
i_vwcb = input.bool(true, 'Enable Volume Weighted Colored Bars', inline='VWC', group=group_volume_weighted_colored_bars, tooltip=tooltip_volume_weighted_colored_bars)
i_vwcbHighThresh = input.float(1.618, 'Thresholds : High ', minval=1., step=.1, inline='VWC', group=group_volume_weighted_colored_bars)
i_vwcbLowThresh = input.float(0.618, 'Low', minval=.1, step=.1, inline='VWC', group=group_volume_weighted_colored_bars)

// Volume Spike - Sign of Exhaustion
i_vSpikeLb = input.bool(true, '🚦 Show Volume Spike Indicator', group=group_volume_spike_sign_of_exhaustion, tooltip=tooltip_volume_spike_sign_of_exhaustion)
i_vSpikeThresh = input.float(4.669, 'Volume Spike Threshold', minval=.1, step=.1, group=group_volume_spike_sign_of_exhaustion)

// High Volatility
i_hATRLb = input.bool(true, '⚡ Show High Volatility Indicator', group=group_high_volatility, tooltip=tooltip_high_volatility)
i_atrLength = input.int(11, 'ATR Length', group=group_high_volatility)
i_atrMult = input.float(2.718, 'ATR Multiplier', minval=.1, step=.1, group=group_high_volatility)

// -Calculations ════════════════════════════════════════════════════════════════════════════════ //
nzVolume = nz(volume)
i_vSMA = ta.sma(nzVolume, i_vSMA_length)

bullCandle = close > open
bearCandle = close < open

risingPrice  = close > close[1]
fallingPrice = close < close[1]

lwstPrice = ta.lowest(low, 3)
hstPrice  = ta.highest(high, 3)

weightedATR = i_atrMult * ta.atr(i_atrLength)
range_1 = math.abs(high - low)

x2 = timenow + 7 * math.round(ta.change(time))

var sProcessing = false
if srLookbackRange == 'Visible Range'
    sProcessing := time >= chart.left_visible_bar_time
else
    sProcessing := time > timenow - i_lenLookback * (timeframe.isintraday ? timeframe.multiplier * 86400000 / 1440 : timeframe.multiplier * 86400000)

// Volume Spike - Sign of Exhaustion
exhaustVol = nzVolume > i_vSpikeThresh * i_vSMA
x1V = ta.valuewhen(exhaustVol, time, 0)

// High Volatility
highVolatility = range_1 > weightedATR
x1hV = ta.valuewhen(highVolatility, time, 0)

// -Plotting ════════════════════════════════════════════════════════════════════════════════════ //

// Volume Weighted Colored Bars
vwcbCol = nzVolume > i_vSMA * i_vwcbHighThresh ?bearCandle ? color.rgb(153, 6, 6) : #056205 :nzVolume < i_vSMA * i_vwcbLowThresh ?  bearCandle ? #FF9800 : #7FFFD4 :na

barcolor(i_vwcb and not na(nzVolume) ? vwcbCol : na, title='Volume Weighted Colored Bars')

// Volume Spike - Sign of Exhaustion

plotchar(i_vSpikeLb and not na(nzVolume) and sProcessing ? exhaustVol : na, 'Exhaustion Bar', '🚦', location.abovebar, size=size.tiny)

// High Volatility
plotchar(i_hATRLb and sProcessing ? highVolatility : na, 'High Volatile Bar', '⚡', location.belowbar, size=size.tiny)

// -Alerts ════════════════════════════════════════════════════════════════════════════════════ //
priceTxt = str.tostring(close, format.mintick)
tickerTxt = syminfo.ticker

if nzVolume > i_vSMA * i_vwcbHighThresh and i_vwcb
    alert(tickerTxt + ' High Volume, price ' + priceTxt)

if nzVolume > i_vSMA * i_vSpikeThresh and i_vSpikeLb
    alert(tickerTxt + ' Volume Spike : sign of exhaustion, huge volume increase detected, price ' + priceTxt)

if ta.crossover(range_1, weightedATR) and i_hATRLb
    alert(tickerTxt + ' High Volatility detected, price ' + priceTxt)

// Additional Impulse/Correction Alerts
if enableImpulseAnalysis and array.size(swingIsImpulse) >= 1 and directionChanged
    string lastSwingType = array.get(swingIsImpulse, array.size(swingIsImpulse)-1) ? "IMPULSE" : "CORRECTION"
    alert(tickerTxt + ' New ' + lastSwingType + ' swing detected, price ' + priceTxt, alert.freq_once_per_bar)
